server {
    listen 80;
    server_name su.guiyunai.fun;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name su.guiyunai.fun;

    # SSL配置
    ssl_certificate /etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/su.guiyunai.fun/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 设置客户端最大请求体大小（用于文件上传）
    client_max_body_size 500M;

    # 文件上传超时配置
    client_body_timeout 300s;
    client_header_timeout 300s;

    # 代理所有请求到后端FastAPI服务器
    location / {
        proxy_pass http://127.0.0.1:4082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://127.0.0.1:4082;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 日志配置
    access_log /var/log/nginx/autoclip_access.log;
    error_log /var/log/nginx/autoclip_error.log;
}
