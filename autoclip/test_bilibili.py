#!/usr/bin/env python3
"""测试哔哩哔哩解析功能"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_bilibili_parse():
    """测试哔哩哔哩视频解析"""
    try:
        from src.utils.bilibili_downloader import BilibiliDownloader, get_bilibili_video_info
        
        # 测试URL - 使用一个公开的视频
        test_urls = [
            "https://www.bilibili.com/video/BV1GJ411x7h7",
            "https://www.bilibili.com/video/BV1xx411c7mD",
            "https://www.bilibili.com/video/BV1s4411E7AR"
        ]
        
        for url in test_urls:
            print(f"\n=== 测试URL: {url} ===")
            try:
                # 尝试不使用浏览器cookies
                downloader = BilibiliDownloader()
                if downloader.validate_bilibili_url(url):
                    print("✅ URL格式验证通过")
                else:
                    print("❌ URL格式验证失败")
                    continue
                
                # 尝试获取视频信息
                print("正在获取视频信息...")
                video_info = await get_bilibili_video_info(url)
                print(f"✅ 成功获取视频信息:")
                print(f"   标题: {video_info.title}")
                print(f"   时长: {video_info.duration}秒")
                print(f"   上传者: {video_info.uploader}")
                break  # 成功一个就够了
                
            except Exception as e:
                print(f"❌ 解析失败: {str(e)}")
                continue
        
        else:
            print("\n❌ 所有测试URL都失败了")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 开始测试哔哩哔哩解析功能...")
    
    # 检查yt-dlp是否安装
    try:
        import yt_dlp
        print(f"✅ yt-dlp已安装，版本: {yt_dlp.version.__version__}")
    except ImportError:
        print("❌ yt-dlp未安装")
        return
    
    # 测试解析功能
    success = await test_bilibili_parse()
    
    if success:
        print("\n🎉 哔哩哔哩解析功能测试成功！")
    else:
        print("\n💥 哔哩哔哩解析功能测试失败！")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN或代理")
        print("3. 更新yt-dlp: pip install -U yt-dlp")
        print("4. 检查视频是否存在或被删除")

if __name__ == "__main__":
    asyncio.run(main())
