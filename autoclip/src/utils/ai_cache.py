"""
AI API结果缓存系统 - 提高处理效率，减少重复API调用
"""
import hashlib
import json
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, Optional, List
import threading

logger = logging.getLogger(__name__)

class AICache:
    """AI API结果缓存管理器"""
    
    def __init__(self, cache_dir: str = "cache/ai_responses", max_age_hours: int = 24):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录
            max_age_hours: 缓存有效期（小时）
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_age_seconds = max_age_hours * 3600
        self._lock = threading.Lock()
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'saves': 0,
            'cleanups': 0
        }
    
    def _generate_cache_key(self, prompt: str, input_data: Any, model: str = None) -> str:
        """
        生成缓存键
        
        Args:
            prompt: 提示词
            input_data: 输入数据
            model: 模型名称
            
        Returns:
            缓存键的哈希值
        """
        # 创建一个包含所有相关信息的字符串
        cache_content = {
            'prompt': prompt,
            'input_data': input_data,
            'model': model or 'default'
        }
        
        # 转换为JSON字符串并生成哈希
        content_str = json.dumps(cache_content, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(content_str.encode('utf-8')).hexdigest()
    
    def _get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.json"
    
    def get(self, prompt: str, input_data: Any, model: str = None) -> Optional[str]:
        """
        从缓存获取结果
        
        Args:
            prompt: 提示词
            input_data: 输入数据
            model: 模型名称
            
        Returns:
            缓存的响应，如果不存在或过期则返回None
        """
        with self._lock:
            cache_key = self._generate_cache_key(prompt, input_data, model)
            cache_file = self._get_cache_file_path(cache_key)
            
            if not cache_file.exists():
                self.stats['misses'] += 1
                return None
            
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                # 检查缓存是否过期
                cache_time = cache_data.get('timestamp', 0)
                if time.time() - cache_time > self.max_age_seconds:
                    # 缓存过期，删除文件
                    cache_file.unlink()
                    self.stats['misses'] += 1
                    logger.debug(f"缓存过期并删除: {cache_key}")
                    return None
                
                # 缓存命中
                self.stats['hits'] += 1
                logger.debug(f"缓存命中: {cache_key}")
                return cache_data.get('response')
                
            except Exception as e:
                logger.error(f"读取缓存失败 {cache_key}: {e}")
                # 删除损坏的缓存文件
                if cache_file.exists():
                    cache_file.unlink()
                self.stats['misses'] += 1
                return None
    
    def set(self, prompt: str, input_data: Any, response: str, model: str = None):
        """
        保存结果到缓存
        
        Args:
            prompt: 提示词
            input_data: 输入数据
            response: API响应
            model: 模型名称
        """
        with self._lock:
            cache_key = self._generate_cache_key(prompt, input_data, model)
            cache_file = self._get_cache_file_path(cache_key)
            
            try:
                cache_data = {
                    'prompt': prompt,
                    'input_data': input_data,
                    'response': response,
                    'model': model,
                    'timestamp': time.time()
                }
                
                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
                self.stats['saves'] += 1
                logger.debug(f"缓存保存: {cache_key}")
                
            except Exception as e:
                logger.error(f"保存缓存失败 {cache_key}: {e}")
    
    def cleanup_expired(self):
        """清理过期的缓存文件"""
        with self._lock:
            current_time = time.time()
            cleaned_count = 0
            
            for cache_file in self.cache_dir.glob("*.json"):
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)
                    
                    cache_time = cache_data.get('timestamp', 0)
                    if current_time - cache_time > self.max_age_seconds:
                        cache_file.unlink()
                        cleaned_count += 1
                        
                except Exception as e:
                    logger.error(f"清理缓存文件失败 {cache_file}: {e}")
                    # 删除损坏的文件
                    cache_file.unlink()
                    cleaned_count += 1
            
            self.stats['cleanups'] += cleaned_count
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个过期缓存文件")
    
    def clear_all(self):
        """清空所有缓存"""
        with self._lock:
            cleared_count = 0
            for cache_file in self.cache_dir.glob("*.json"):
                try:
                    cache_file.unlink()
                    cleared_count += 1
                except Exception as e:
                    logger.error(f"删除缓存文件失败 {cache_file}: {e}")
            
            logger.info(f"清空了 {cleared_count} 个缓存文件")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                **self.stats,
                'total_requests': total_requests,
                'hit_rate_percent': round(hit_rate, 2),
                'cache_files': len(list(self.cache_dir.glob("*.json")))
            }
    
    def get_cache_size(self) -> Dict[str, Any]:
        """获取缓存大小信息"""
        total_size = 0
        file_count = 0
        
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                total_size += cache_file.stat().st_size
                file_count += 1
            except Exception:
                pass
        
        return {
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'file_count': file_count
        }

# 全局缓存实例
_global_cache = None
_cache_lock = threading.Lock()

def get_ai_cache(cache_dir: str = "cache/ai_responses", max_age_hours: int = 24) -> AICache:
    """
    获取全局AI缓存实例
    
    Args:
        cache_dir: 缓存目录
        max_age_hours: 缓存有效期（小时）
        
    Returns:
        AICache实例
    """
    global _global_cache
    
    with _cache_lock:
        if _global_cache is None:
            _global_cache = AICache(cache_dir, max_age_hours)
            # 启动时清理过期缓存
            _global_cache.cleanup_expired()
    
    return _global_cache
