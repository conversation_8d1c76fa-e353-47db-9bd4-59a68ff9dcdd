"""
异步AI客户端 - 支持并行API调用优化
"""
import asyncio
import json
import logging
import time
from typing import Dict, Any, List, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from .siliconflow_client import SiliconFlowClient
from .dashscope_client import Dash<PERSON>cope<PERSON>lient
from .ai_cache import get_ai_cache

logger = logging.getLogger(__name__)

class AsyncAIClient:
    """异步AI客户端，支持并行API调用"""
    
    def __init__(self, api_provider: str = "siliconflow", **kwargs):
        """
        初始化异步AI客户端
        
        Args:
            api_provider: API提供商 (siliconflow 或 dashscope)
            **kwargs: 传递给具体客户端的参数
        """
        self.api_provider = api_provider
        self.max_concurrent_requests = 5  # 最大并发请求数
        self.request_delay = 0.1  # 请求间隔（秒）

        # 创建底层客户端
        if api_provider == "siliconflow":
            self.client = SiliconFlowClient(**kwargs)
        elif api_provider == "dashscope":
            # 暂时使用SiliconFlowClient作为fallback
            self.client = SiliconFlowClient(**kwargs)
        else:
            raise ValueError(f"不支持的API提供商: {api_provider}")

        # 线程池用于并行请求
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_requests)
        self._request_lock = threading.Lock()
        self._last_request_time = 0

        # 缓存系统
        self.cache = get_ai_cache()
        self.enable_cache = kwargs.get('enable_cache', True)
    
    def _rate_limited_call(self, prompt: str, input_data: Any = None) -> str:
        """
        带速率限制和缓存的API调用

        Args:
            prompt: 提示词
            input_data: 输入数据

        Returns:
            API响应
        """
        # 尝试从缓存获取结果
        if self.enable_cache:
            cached_response = self.cache.get(prompt, input_data, self.api_provider)
            if cached_response:
                logger.debug("使用缓存响应")
                return cached_response

        # 缓存未命中，进行API调用
        with self._request_lock:
            # 实现简单的速率限制
            current_time = time.time()
            time_since_last = current_time - self._last_request_time
            if time_since_last < self.request_delay:
                time.sleep(self.request_delay - time_since_last)
            self._last_request_time = time.time()

        # 调用API
        response = self.client.call_with_retry(prompt, input_data)

        # 保存到缓存
        if self.enable_cache and response:
            self.cache.set(prompt, input_data, response, self.api_provider)

        return response
    
    def parallel_call(self, 
                     requests: List[Dict[str, Any]], 
                     progress_callback: Optional[Callable[[int, int], None]] = None) -> List[str]:
        """
        并行执行多个API调用
        
        Args:
            requests: 请求列表，每个请求包含 {'prompt': str, 'input_data': Any}
            progress_callback: 进度回调函数 (completed, total)
            
        Returns:
            响应列表，与请求列表顺序对应
        """
        if not requests:
            return []
        
        logger.info(f"开始并行执行 {len(requests)} 个API请求，最大并发数: {self.max_concurrent_requests}")
        
        # 提交所有任务
        future_to_index = {}
        for i, request in enumerate(requests):
            future = self.executor.submit(
                self._rate_limited_call,
                request['prompt'],
                request.get('input_data')
            )
            future_to_index[future] = i
        
        # 收集结果
        results = [None] * len(requests)
        completed = 0
        
        for future in as_completed(future_to_index):
            index = future_to_index[future]
            try:
                result = future.result()
                results[index] = result
                completed += 1
                
                if progress_callback:
                    progress_callback(completed, len(requests))
                    
                logger.debug(f"API请求 {index + 1}/{len(requests)} 完成")
                
            except Exception as e:
                logger.error(f"API请求 {index + 1} 失败: {e}")
                results[index] = ""  # 失败时返回空字符串
                completed += 1
                
                if progress_callback:
                    progress_callback(completed, len(requests))
        
        logger.info(f"并行API调用完成，成功: {sum(1 for r in results if r)}/{len(results)}")
        return results
    
    def batch_process_chunks(self, 
                           chunks: List[Dict], 
                           prompt: str,
                           progress_callback: Optional[Callable[[int, int], None]] = None) -> List[Dict]:
        """
        批量处理文本块
        
        Args:
            chunks: 文本块列表
            prompt: 处理提示词
            progress_callback: 进度回调函数
            
        Returns:
            处理结果列表
        """
        if not chunks:
            return []
        
        # 准备并行请求
        requests = []
        for chunk in chunks:
            requests.append({
                'prompt': prompt,
                'input_data': chunk
            })
        
        # 执行并行调用
        responses = self.parallel_call(requests, progress_callback)
        
        # 解析结果
        results = []
        for i, (chunk, response) in enumerate(zip(chunks, responses)):
            if response:
                try:
                    # 尝试解析JSON响应
                    if hasattr(self.client, 'parse_json_response'):
                        parsed_result = self.client.parse_json_response(response)
                    else:
                        # 简单的JSON解析
                        parsed_result = json.loads(response)
                    
                    results.append({
                        'chunk_index': chunk.get('chunk_index', i),
                        'result': parsed_result,
                        'success': True
                    })
                except Exception as e:
                    logger.error(f"解析块 {i} 的响应失败: {e}")
                    results.append({
                        'chunk_index': chunk.get('chunk_index', i),
                        'result': None,
                        'success': False,
                        'error': str(e)
                    })
            else:
                results.append({
                    'chunk_index': chunk.get('chunk_index', i),
                    'result': None,
                    'success': False,
                    'error': "API调用失败"
                })
        
        return results

    def batch_merge_small_requests(self, requests: List[Dict[str, Any]], max_batch_size: int = 3) -> List[Dict[str, Any]]:
        """
        将小的请求合并为批量请求以提高效率

        Args:
            requests: 原始请求列表
            max_batch_size: 最大批量大小

        Returns:
            合并后的请求列表
        """
        if len(requests) <= 1:
            return requests

        # 简单的批量合并策略：将相同prompt的小请求合并
        merged_requests = []
        current_batch = []
        current_prompt = None

        for request in requests:
            prompt = request['prompt']

            # 如果是新的prompt或批量已满，开始新批量
            if current_prompt != prompt or len(current_batch) >= max_batch_size:
                if current_batch:
                    # 创建合并的请求
                    if len(current_batch) == 1:
                        merged_requests.append(current_batch[0])
                    else:
                        # 合并多个输入数据
                        merged_input = [req['input_data'] for req in current_batch]
                        merged_requests.append({
                            'prompt': current_prompt,
                            'input_data': merged_input,
                            'is_batch': True,
                            'original_count': len(current_batch)
                        })

                current_batch = [request]
                current_prompt = prompt
            else:
                current_batch.append(request)

        # 处理最后一个批量
        if current_batch:
            if len(current_batch) == 1:
                merged_requests.append(current_batch[0])
            else:
                merged_input = [req['input_data'] for req in current_batch]
                merged_requests.append({
                    'prompt': current_prompt,
                    'input_data': merged_input,
                    'is_batch': True,
                    'original_count': len(current_batch)
                })

        logger.info(f"批量合并: {len(requests)} 个请求 → {len(merged_requests)} 个批量请求")
        return merged_requests

    def close(self):
        """关闭客户端，清理资源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

# 全局异步客户端实例
_async_client = None
_client_lock = threading.Lock()

def get_async_ai_client(api_provider: str = "siliconflow", **kwargs) -> AsyncAIClient:
    """
    获取全局异步AI客户端实例
    
    Args:
        api_provider: API提供商
        **kwargs: 客户端参数
        
    Returns:
        AsyncAIClient实例
    """
    global _async_client
    
    with _client_lock:
        if _async_client is None or _async_client.api_provider != api_provider:
            if _async_client:
                _async_client.close()
            _async_client = AsyncAIClient(api_provider, **kwargs)
    
    return _async_client
