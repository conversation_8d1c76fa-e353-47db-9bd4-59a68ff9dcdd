#!/usr/bin/env python3
"""
测试YouTube API端点
"""

import requests
import json

def test_youtube_api():
    """测试YouTube API端点"""
    
    base_url = "http://localhost:4081"
    
    # 测试YouTube URL
    test_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Rick Roll
        "https://youtu.be/dQw4w9WgXcQ",  # 短链接
        "https://www.bilibili.com/video/BV1xx411c7mD",  # B站测试
    ]
    
    for url in test_urls:
        print(f"\n测试URL: {url}")
        
        try:
            # 测试新的通用视频解析API
            response = requests.post(
                f"{base_url}/api/video/parse",
                data={"url": url},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    video_info = data.get("video_info", {})
                    print(f"  ✅ 解析成功")
                    print(f"  标题: {video_info.get('title', 'N/A')}")
                    print(f"  时长: {video_info.get('duration', 'N/A')}秒")
                    print(f"  上传者: {video_info.get('uploader', 'N/A')}")
                else:
                    print(f"  ❌ API返回失败: {data}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                print(f"  错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"  ⏰ 请求超时")
        except Exception as e:
            print(f"  ❌ 请求失败: {e}")

if __name__ == "__main__":
    print("🎬 YouTube API测试")
    print("=" * 50)
    test_youtube_api()
    print("\n✅ 测试完成")
