#!/usr/bin/env python3
"""
测试后端服务启动
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    print("🔍 检查Python环境...")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    
    print("\n🔍 检查依赖包...")
    import uvicorn
    print(f"✅ uvicorn: {uvicorn.__version__}")
    
    import fastapi
    print(f"✅ fastapi: {fastapi.__version__}")
    
    print("\n🔍 检查项目模块...")
    from src.config import Config
    print("✅ 配置模块加载成功")
    
    from src.utils.project_manager import ProjectManager
    print("✅ 项目管理器模块加载成功")
    
    print("\n🔍 检查端口占用...")
    import socket
    
    def check_port(port):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            sock.bind(('0.0.0.0', port))
            sock.close()
            return True
        except OSError:
            return False
    
    for port in [4080, 4081]:
        if check_port(port):
            print(f"✅ 端口 {port} 可用")
        else:
            print(f"❌ 端口 {port} 被占用")
    
    print("\n🚀 尝试启动服务器...")
    
    # 导入后端服务器
    import backend_server
    print("✅ 后端服务器模块加载成功")
    
    print("✅ 所有检查通过，可以启动服务器")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
