[{"id": "2617d8ad-1c93-4845-8c8c-d3037862772a", "name": "【AI创业】18个月从0到200万用户：如何打造顶尖AI数据分析工具 | Julius AI创始人Rahul Sonwalkar", "video_path": "uploads/2617d8ad-1c93-4845-8c8c-d3037862772a/input/input.mp4", "status": "completed", "created_at": "2025-07-10T23:45:49.649376", "updated_at": "2025-07-10T23:47:25.952079", "video_category": "default", "clips": [{"id": "1", "title": null, "start_time": "00:00:00,000", "end_time": "00:01:22,080", "final_score": 0.9, "recommend_reason": "真实创业故事引爆共鸣，情感张力强，极具感染力。", "generated_title": "从辞职到创立Julius，一段关于紧张、恐惧与转化的创业真实历程", "outline": "<PERSON>的创立背景与个人经历", "content": ["主播辞职创业，专注开发Julius的过程", "创业初期的心理状态：紧张、恐惧与积极心态的转化", "Julius的产品定位：专注于智能数据分析和可视化工具"], "chunk_index": 0}, {"id": "2", "title": null, "start_time": "00:01:22,080", "end_time": "00:03:54,520", "final_score": 0.85, "recommend_reason": "观点鲜明，精准切中AI产品发展痛点，信息价值高。", "generated_title": "初创公司为何不能贪大？一文讲透专注型AI产品的真正优势", "outline": "专注型AI产品的优势与市场反馈", "content": ["强调初创公司应聚焦特定领域而非开发通用AI工具", "用户从Chat GPT转向Julius的使用体验提升", "Julius在数据处理深度、图表专业性、协作功能上的优势"], "chunk_index": 0}, {"id": "3", "title": null, "start_time": "00:03:54,520", "end_time": "00:05:59,169", "final_score": 0.91, "recommend_reason": "干货密集，洞见深刻，值得创业者反复品读。", "generated_title": "用户为什么不用你的产品？一位创业者关于高频痛点的深刻反思", "outline": "创业经验与产品设计哲学", "content": ["来自黑客马拉松的早期项目经验与用户留存教训", "解决用户高频痛点的重要性：低频问题无法形成持续使用", "初创公司 vs 大公司的创新机制差异：一个同意 vs 多个反对"], "chunk_index": 0}, {"id": "4", "title": null, "start_time": "00:05:59,169", "end_time": "00:07:50,540", "final_score": 0.88, "recommend_reason": "职场转型+创业抉择双线叙事，代入感极强。", "generated_title": "从Uber构想到离职创业，一个关于通勤出行的创新受阻故事", "outline": "Uber内部创新尝试与离职决定", "content": ["在Uber提出的通勤出行产品构想及其受阻过程", "工程负责人未批准项目，促使主播选择离职创业", "快速试错理念：周末原型推动想法验证并加速决策"], "chunk_index": 0}, {"id": "5", "title": null, "start_time": "00:07:50,540", "end_time": "00:09:03,020", "final_score": 0.83, "recommend_reason": "实战方法论清晰，适合初创团队借鉴学习。", "generated_title": "产品别等完美再发布，创始人亲述‘barely可用’的早期迭代策略", "outline": "快速失败与产品迭代策略", "content": ["对创始人过度延迟发布产品的担忧", "提前发布“barely可用”的产品以获取早期反馈", "NBA数据分析工具Hoops GPD的快速开发与验证过程"], "chunk_index": 0}, {"id": "6", "title": null, "start_time": "00:09:03,020", "end_time": "00:09:25,720", "final_score": 0.87, "recommend_reason": "真实增长挑战+应对策略，具备行业参考价值。", "generated_title": "插件商店关闭差点让Julius停滞，他们如何靠分享功能逆转增长？", "outline": "用户增长危机与应对策略", "content": ["Julius早期通过Chat GPT插件商店快速增长", "OpenAI宣布关闭插件商店带来的用户来源断裂危机", "推出分享功能促进用户自发传播与口碑增长"], "chunk_index": 0}, {"id": "7", "title": null, "start_time": "00:09:25,720", "end_time": "00:09:40,690", "final_score": 0.93, "recommend_reason": "深度复盘引发思考，结尾升华，极具启发意义。", "generated_title": "失败真的可怕吗？这位创始人用经历告诉你：它只是让你少走弯路", "outline": "总结反思与未来设想", "content": ["回顾整个创业过程中学到的关键教训", "对失败的价值认知：失败提供明确方向排除干扰", "如果重新开始是否会有不同选择？答案是否定的"], "chunk_index": 0}], "collections": [{"id": "1", "collection_title": "创业心路历程", "collection_summary": "真实讲述创业者从想法到实践的心路历程，启发思考。", "clip_ids": ["7", "1", "4"], "collection_type": "ai_recommended", "created_at": null}, {"id": "2", "collection_title": "初创产品策略", "collection_summary": "聚焦初创公司在产品开发中的关键决策与实战经验。", "clip_ids": ["2", "5", "3"], "collection_type": "ai_recommended", "created_at": null}, {"id": "3", "collection_title": "增长与转型挑战", "collection_summary": "解析初创企业在增长瓶颈与业务转型中的应对之道。", "clip_ids": ["6", "2"], "collection_type": "ai_recommended", "created_at": null}], "current_step": null, "total_steps": 6, "error_message": null}, {"id": "fa5d00eb-4043-4466-be16-4e567b78a59d", "name": "agentflow", "video_path": "uploads/fa5d00eb-4043-4466-be16-4e567b78a59d/input/input.mp4", "status": "completed", "created_at": "2025-08-05T08:22:09.270522", "updated_at": "2025-08-05T08:28:23.929705", "video_category": "experience", "clips": [], "collections": [], "current_step": null, "total_steps": 6, "error_message": null}]