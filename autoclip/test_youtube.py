#!/usr/bin/env python3
"""
测试YouTube视频解析功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.utils.bilibili_downloader import BilibiliDownloader

async def test_youtube_parsing():
    """测试YouTube视频解析"""
    
    # 测试YouTube URL
    test_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Rick Roll
        "https://youtu.be/dQw4w9WgXcQ",  # 短链接
        "https://www.youtube.com/watch?v=jNQXAC9IVRw",  # 另一个测试视频
    ]
    
    downloader = BilibiliDownloader()
    
    for url in test_urls:
        print(f"\n测试URL: {url}")
        
        # 测试URL验证
        is_youtube = downloader.validate_youtube_url(url)
        is_valid = downloader.validate_video_url(url)
        
        print(f"  YouTube URL验证: {is_youtube}")
        print(f"  通用URL验证: {is_valid}")
        
        if is_valid:
            try:
                # 测试获取视频信息
                print("  正在获取视频信息...")
                video_info = await downloader.get_video_info(url)
                print(f"  标题: {video_info.title}")
                print(f"  时长: {video_info.duration}秒")
                print(f"  上传者: {video_info.uploader}")
                print(f"  观看次数: {video_info.view_count}")
                print("  ✅ 解析成功")
            except Exception as e:
                print(f"  ❌ 解析失败: {e}")
        else:
            print("  ❌ URL验证失败")

if __name__ == "__main__":
    print("🎬 YouTube视频解析测试")
    print("=" * 50)
    
    try:
        asyncio.run(test_youtube_parsing())
        print("\n✅ 测试完成")
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
