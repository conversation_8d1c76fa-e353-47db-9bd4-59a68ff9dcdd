server {
    listen 80;
    server_name su.guiyunai.fun;

    # 设置客户端最大请求体大小（用于文件上传）
    client_max_body_size 500M;

    # API请求代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:4080/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # 静态资源代理到后端
    location /static/ {
        proxy_pass http://127.0.0.1:4080/static/;
        proxy_set_header Host $host;
    }

    location /uploads/ {
        proxy_pass http://127.0.0.1:4080/uploads/;
        proxy_set_header Host $host;
    }

    # 前端资源代理到后端
    location /assets/ {
        proxy_pass http://127.0.0.1:4080/assets/;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 其他所有请求代理到后端（SPA支持）
    location / {
        proxy_pass http://127.0.0.1:4080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 日志配置
    access_log /var/log/nginx/autoclip_access.log;
    error_log /var/log/nginx/autoclip_error.log;
}
