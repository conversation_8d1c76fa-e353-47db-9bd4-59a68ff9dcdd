#!/bin/bash

# AutoClip项目 - 1号备份快速恢复脚本
# 使用方法: sudo bash restore.sh

echo "🔄 开始恢复AutoClip项目到1号备份状态..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用sudo运行此脚本"
    exit 1
fi

# 设置路径
BACKUP_DIR="/www/wwwroot/su.guiyunai.fun/backups/backup-1-baseline"
PROJECT_DIR="/www/wwwroot/su.guiyunai.fun"

echo "📍 备份路径: $BACKUP_DIR"
echo "📍 项目路径: $PROJECT_DIR"

# 1. 停止当前服务
echo "🛑 停止当前服务..."
systemctl stop nginx 2>/dev/null || echo "Nginx未运行"
pkill -f "python.*backend_server" 2>/dev/null || echo "后端服务未运行"
pkill -f "vite" 2>/dev/null || echo "前端服务未运行"
sleep 3

# 2. 备份当前项目（以防万一）
if [ -d "$PROJECT_DIR/autoclip" ]; then
    echo "💾 备份当前项目到临时目录..."
    mv "$PROJECT_DIR/autoclip" "$PROJECT_DIR/autoclip.backup.$(date +%Y%m%d_%H%M%S)" 2>/dev/null
fi

# 3. 恢复项目文件
echo "📂 恢复项目文件..."
cp -r "$BACKUP_DIR/autoclip" "$PROJECT_DIR/"
if [ $? -eq 0 ]; then
    echo "✅ 项目文件恢复成功"
else
    echo "❌ 项目文件恢复失败"
    exit 1
fi

# 4. 恢复Nginx配置
echo "🌐 恢复Nginx配置..."
cp "$BACKUP_DIR/nginx-autoclip.conf" /etc/nginx/sites-available/autoclip
ln -sf /etc/nginx/sites-available/autoclip /etc/nginx/sites-enabled/
nginx -t
if [ $? -eq 0 ]; then
    echo "✅ Nginx配置恢复成功"
    systemctl start nginx
else
    echo "❌ Nginx配置有误"
    exit 1
fi

# 5. 设置权限
echo "🔐 设置文件权限..."
chown -R root:root "$PROJECT_DIR/autoclip"
chmod +x "$PROJECT_DIR/autoclip/start_dev.sh"
chmod +x "$PROJECT_DIR/autoclip/start_production.sh"

# 6. 启动服务
echo "🚀 启动服务..."
cd "$PROJECT_DIR/autoclip"

# 启动后端
echo "启动后端服务 (端口4080)..."
source venv/bin/activate
nohup python backend_server.py > backend.log 2>&1 &
BACKEND_PID=$!
echo "后端PID: $BACKEND_PID"

# 等待后端启动
sleep 5

# 检查后端是否启动成功
if curl -s http://localhost:4080/api/browsers/detect > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    exit 1
fi

echo ""
echo "🎉 恢复完成！"
echo ""
echo "📋 服务状态:"
echo "  - 后端API: http://localhost:4080"
echo "  - 域名访问: http://su.guiyunai.fun"
echo "  - 前端开发: 需要手动启动 'cd frontend && npm run dev'"
echo ""
echo "🔍 验证命令:"
echo "  curl http://localhost:4080/api/browsers/detect"
echo "  curl http://su.guiyunai.fun/api/projects"
echo ""
echo "📝 日志文件:"
echo "  - 后端日志: $PROJECT_DIR/autoclip/backend.log"
echo "  - Nginx日志: /var/log/nginx/autoclip_*.log"
