# 🗂️ AutoClip项目 - 1号备份（基准版本）

## 📅 备份信息
- **备份时间**: 2025-08-04 20:15
- **备份版本**: 1号备份 (baseline)
- **备份状态**: 前后端正常运行，端口配置完成
- **创建原因**: 系统稳定运行的基准版本，用于紧急回滚

## 🔧 系统配置状态

### 端口配置
- **后端端口**: 4080 (FastAPI + Uvicorn)
- **前端开发端口**: 4081 (Vite开发服务器)
- **Nginx代理**: 80 → 4080
- **域名访问**: http://su.guiyunai.fun

### 服务状态
- ✅ 后端API服务正常运行
- ✅ 前端开发服务器正常运行
- ✅ Nginx反向代理配置正确
- ✅ 域名访问正常工作
- ✅ 所有API端点测试通过

## 📁 备份内容

### 1. 完整项目代码
```
autoclip/
├── backend_server.py          # 后端主服务器 (端口4080)
├── src/                       # 后端源码
├── frontend/                  # 前端项目
│   ├── src/                   # 前端源码
│   ├── vite.config.ts         # Vite配置 (端口4081)
│   └── dist/                  # 构建文件
├── data/                      # 数据文件
├── uploads/                   # 上传文件
├── venv/                      # Python虚拟环境
├── requirements.txt           # Python依赖
├── start_dev.sh              # 开发环境启动脚本
└── start_production.sh       # 生产环境启动脚本
```

### 2. 配置文件
- `nginx-autoclip.conf` - Nginx配置文件
- 前端API配置 (动态端口检测)
- 后端CORS配置 (支持4081端口)

## 🚀 恢复步骤

### 紧急回滚操作
```bash
# 1. 停止当前服务
sudo systemctl stop nginx
pkill -f "python.*backend_server"
pkill -f "vite"

# 2. 恢复项目文件
cd /www/wwwroot/su.guiyunai.fun
rm -rf autoclip
cp -r backups/backup-1-baseline/autoclip ./

# 3. 恢复Nginx配置
sudo cp backups/backup-1-baseline/nginx-autoclip.conf /etc/nginx/sites-available/autoclip
sudo nginx -t
sudo systemctl start nginx

# 4. 启动服务
cd autoclip
source venv/bin/activate
python backend_server.py &
cd frontend && npm run dev &
```

## ✅ 验证步骤
1. 检查后端: `curl http://localhost:4080/api/browsers/detect`
2. 检查前端: `curl http://localhost:4081/`
3. 检查域名: `curl http://su.guiyunai.fun/api/projects`
4. 检查Nginx: `sudo nginx -t`

## 📋 关键配置

### 后端配置 (backend_server.py)
- 端口: 4080
- CORS: 支持4081端口
- API路径: /api/*

### 前端配置 (vite.config.ts)
- 开发端口: 4081
- API代理: /api → http://localhost:4080

### Nginx配置
- 监听端口: 80
- 代理目标: http://127.0.0.1:4080
- 域名: su.guiyunai.fun

## ⚠️ 重要提醒
- 此备份为基准版本，系统运行稳定
- 恢复前请确保停止所有相关服务
- 恢复后需要重新安装npm依赖: `cd frontend && npm install`
- Python虚拟环境已包含在备份中
- 数据文件和上传文件已完整备份

## 🔒 备份策略
- **备份编号**: 1号备份
- **备份类型**: 完整备份
- **保留期限**: 永久保留
- **更新策略**: 仅在用户明确命令下创建新备份
