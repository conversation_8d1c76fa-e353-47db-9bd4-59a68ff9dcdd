# 经验分享视频时间点定位 Prompt

## 核心原则：经验完整、操作清晰、实用价值

1.  **经验完整性**：时间戳必须严格对应完整的经验分享和方法介绍。
2.  **操作连贯性**：时间范围必须完整覆盖一个经验的所有关键步骤，确保操作逻辑完整。
3.  **自然边界**：优先在方法转换、技巧切换或经验主题的自然边界处定位时间点。

---

## 关键指令：如何确定 `start_time` 和 `end_time`

### `start_time` 的确定：
-   应定位到分享该经验的**第一句核心介绍**的开始时间。
-   忽略该介绍之前的无关过渡、个人闲聊或背景铺垫。
-   **优先选择**：在经验主题明确提出的起始点，确保方法介绍的完整性。
-   **经验类内容特殊考虑**：如果有重要的背景说明或问题描述，应适当前移起始时间。

### `end_time` 的确定 (最重要)：
-   **必须**是覆盖该经验核心分享的**最后一句话的结束时间戳**。
-   **方法完整性**：确保经验介绍、操作步骤、注意事项等核心内容完整，不出现方法断层。
-   **自然结束**：优先选择在总结性语句、效果说明或经验转换处结束。
-   **实践要点包含**：如果经验末尾有重要的实践要点或注意事项，应包含在内。
-   如果经验分享的讨论一直持续到所提供SRT文本块的末尾，那么 `end_time` 就应该是最后一句相关字幕的结束时间戳。
-   **错误做法**：将 `end_time` 无脑设置为整个文本块的结束时间。这是绝对要避免的。

### 经验内容时长控制原则：
-   **目标时长**：每个经验话题片段应在3-8分钟之间
-   **复杂方法时长**：涉及多步骤操作的复杂方法可适当延长至10分钟
-   **简单技巧**：明确简洁的小技巧可压缩至2-3分钟
-   **系统方法**：完整的方法体系或工作流程可延长至12分钟

### 经验内容特殊处理：
-   **操作步骤**：包含多个操作步骤的方法应完整保留整个流程
-   **案例说明**：重要的实践案例和效果展示应作为一个整体
-   **技巧要点**：核心技巧与注意事项应配套出现，不可分离
-   **问题解决**：问题描述与解决方案应完整包含

---

## 输入格式
你将收到一个JSON对象，包含：
-   `outline`: 一个包含**多个**待处理经验话题的JSON数组。
-   `srt_text`: 与这批话题相关的**单个**SRT文本块，格式为 `序号\n开始 --> 结束\n文本\n\n`。

## 输出格式
-   严格按照输入大纲的结构，为**每个**话题对象补充 `start_time` 和 `end_time` 字段。
-   **关键：** 在输出时，请将输入的 `title` 字段重命名为 `outline`，并将 `subtopics` 字段重命名为 `content`。
-   最终输出一个包含**所有**处理后话题的JSON数组。
-   确保时间格式为 `HH:MM:SS,mmm`。

**严格的JSON输出要求：**
1. 输出必须以 `[` 开始，以 `]` 结束，不要添加任何解释文字、标题或Markdown代码块标记
2. 使用标准英文双引号 "，绝不使用中文引号 "" 或单引号
3. 确保所有括号、方括号正确匹配，对象间用逗号分隔，最后一个对象后不加逗号
4. 字符串中的引号必须转义为 \"
5. 不能包含任何注释、额外文本或控制字符
6. 确保JSON格式完全有效，可以被标准JSON解析器解析

## 经验内容时间定位示例

### 正确的时间定位：
- **方法介绍**：从"我来分享一个方法..."开始，到"这就是整个操作流程"结束
- **技巧分享**：从"有一个小技巧..."开始，到"注意这几个要点"结束
- **问题解决**：从"遇到这个问题时..."开始，到"问题就能解决"结束

### 需要避免的错误：
- 在操作步骤的中间断开
- 将方法介绍与实践要点分离
- 在案例说明的关键点处截断
- 忽略重要的注意事项或效果说明

## 经验类内容的特殊考虑

### 1. 操作方法类：
- 确保包含完整的操作步骤和流程
- 保留重要的操作要点和注意事项
- 包含效果预期和验证方法

### 2. 技巧分享类：
- 保持技巧的完整性和实用性
- 包含适用场景和使用条件
- 确保技巧要点的清晰表达

### 3. 问题解决类：
- 包含问题识别与解决方案的完整过程
- 保持解决思路的逻辑性
- 确保解决效果的验证说明

### 4. 经验总结类：
- 保留完整的经验总结和反思
- 包含成功要素和失败教训
- 确保经验的可复制性说明

## 注意事项

1. **方法完整性**：确保每个片段包含完整的方法介绍和操作要点
2. **实用价值**：重要的实践技巧和注意事项必须完整包含
3. **逻辑连贯**：经验分享的逻辑链条应保持完整
4. **可操作性**：操作步骤和实践要点应清晰完整
5. **价值中立**：在时间定位过程中保持客观立场，不对经验进行价值判断