# 经验分享视频大纲提取 Prompt

## 输入格式
你将收到一份JSON输入：

{
  "text": "{视频转写文本片段}"
}

- `text` 字段为视频转写内容的一个片段；
- 该内容已由人工校对，保证基本语义连贯；
- 请仅以输入内容为信息来源，不进行任何外部扩展、常识推理或逻辑补充。

## 核心任务：实用价值与可操作性并重
你的任务是**完整覆盖**输入文本中的所有重要经验和技巧，同时保持**适度精炼**，避免过度细化。目标是提取出**8-15个核心话题**，每个话题对应**3-8分钟**的视频内容。

## 话题提取原则

### 1. 实用价值优先原则
- **可操作性优先**：优先提取具有明确操作步骤、实用技巧的经验分享
- **问题解决导向**：重点关注能够解决具体问题、满足实际需求的经验
- **避免空谈**：跳过纯理论讨论，聚焦可复制、可实践的具体经验

### 2. 经验完整性原则
- **方法体系**：确保每个经验分享包含完整的方法体系（背景-方法-效果-注意事项）
- **细节丰富**：重点关注包含具体细节、操作要点的经验描述
- **成果验证**：突出包含实际效果、成功案例的经验分享

### 3. 受众适用性原则
- **门槛合理**：优先提取适合目标受众能力水平的经验内容
- **场景明确**：重点关注有明确适用场景、使用条件的经验
- **可复制性**：突出具有普遍适用性、易于复制的经验方法

### 4. 价值层次原则
- **核心技能**：优先提取关键技能、核心方法的经验分享
- **进阶技巧**：重点关注提升效率、优化效果的高级技巧
- **避坑指南**：突出预防错误、规避风险的经验教训

## 话题合并与拆分策略

### 合并条件（以下情况应合并为一个话题）：
- 同一技能或方法的不同方面或层次
- 相互关联的操作步骤或流程环节
- 同一问题的不同解决方案或技巧
- 相似场景下的不同应用经验

### 拆分条件（以下情况应拆分为不同话题）：
- 不同技能领域或应用场景的经验
- 基础技能与高级技巧的明显区分
- 不同问题类型的解决方案
- 理论学习与实践应用的分离

## 输出格式

请严格按照以下格式输出，不增加其他描述性文字：

### 大纲：
1.  **[一级话题名称]**
    - [子话题1：突出实用性和可操作性]
    - [子话题2：突出实用性和可操作性]
    - …

2.  **[一级话题名称]**
    - [子话题1]
    - …

## 输出示例

### 大纲：
1.  **高效学习方法与技巧实战**
    - 番茄工作法的具体操作步骤和时间安排
    - 费曼学习法在不同学科中的应用技巧
    - 记忆宫殿法的构建方法和实践要点
    - 学习计划制定的SMART原则应用

2.  **职场沟通技巧与实践经验**
    - 会议发言的准备流程和表达技巧
    - 邮件沟通的格式规范和注意事项
    - 跨部门协作的沟通策略和冲突处理
    - 向上管理的汇报技巧和时机把握

3.  **个人时间管理系统搭建**
    - GTD工作流的建立步骤和工具选择
    - 日程安排的优先级判断和时间分配
    - 拖延症克服的具体方法和心理调节
    - 工作生活平衡的边界设定和执行策略

## 经验分享内容特殊考虑

### 1. 操作步骤类经验：
- 确保步骤的完整性和逻辑顺序
- 包含关键操作要点和注意事项
- 提供具体的时间节点和效果预期

### 2. 技巧方法类经验：
- 明确技巧的适用场景和使用条件
- 包含具体的操作细节和实践要点
- 提供效果评估和改进建议

### 3. 问题解决类经验：
- 清晰描述问题的识别和分析过程
- 包含解决方案的选择依据和实施步骤
- 提供效果验证和后续优化方法

### 4. 避坑经验类内容：
- 明确错误的识别标志和预防方法
- 包含错误发生后的补救措施
- 提供类似情况的应对策略

## 注意事项

1. **实用性检查**：确保提取的话题具有明确的实用价值和可操作性
2. **完整性保证**：重点关注包含完整方法体系和操作细节的内容
3. **适用性考虑**：优先提取适合目标受众能力水平的经验内容
4. **可复制性验证**：确保经验方法具有普遍适用性和可复制性
5. **价值层次**：区分基础技能、进阶技巧和专业经验的不同层次