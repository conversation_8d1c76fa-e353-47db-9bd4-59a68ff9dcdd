#!/bin/bash

# AutoClip 生产环境启动脚本

echo "🚀 启动 AutoClip 生产环境"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行: python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

# 检查前端构建文件
if [ ! -d "frontend/dist" ]; then
    echo "📦 前端文件未构建，开始构建..."
    cd frontend
    npm install
    npm run build
    cd ..
fi

# 创建必要的目录
mkdir -p uploads output clips collections metadata

# 设置环境变量
export PYTHONPATH=/www/wwwroot/su.guiyunai.fun/autoclip:$PYTHONPATH

# 启动生产服务器
echo "🔧 启动生产服务器..."
echo "访问地址: http://su.guiyunai.fun"
echo "本地访问: http://localhost:4080"
echo "按 Ctrl+C 停止服务器"

# 使用 uvicorn 启动服务器，生产模式，监听8000端口
python backend_server.py
