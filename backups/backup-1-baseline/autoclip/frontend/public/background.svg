<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主背景渐变 -->
    <radialGradient id="mainGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" stop-color="#1a1a2e" stop-opacity="1"/>
      <stop offset="50%" stop-color="#16213e" stop-opacity="1"/>
      <stop offset="100%" stop-color="#0f0f23" stop-opacity="1"/>
    </radialGradient>
    
    <!-- 光效渐变 -->
    <radialGradient id="lightGradient1" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#4facfe" stop-opacity="0.8"/>
      <stop offset="50%" stop-color="#00f2fe" stop-opacity="0.4"/>
      <stop offset="100%" stop-color="transparent" stop-opacity="0"/>
    </radialGradient>
    
    <radialGradient id="lightGradient2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#667eea" stop-opacity="0.6"/>
      <stop offset="50%" stop-color="#764ba2" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="transparent" stop-opacity="0"/>
    </radialGradient>
    
    <!-- 文字渐变 -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4facfe"/>
      <stop offset="50%" stop-color="#00f2fe"/>
      <stop offset="100%" stop-color="#667eea"/>
    </linearGradient>
    
    <!-- 发光滤镜 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 文字发光滤镜 -->
    <filter id="textGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 网格图案 -->
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="#4facfe" stroke-width="0.5" opacity="0.1"/>
    </pattern>
  </defs>
  
  <!-- 主背景 -->
  <rect width="1920" height="1080" fill="url(#mainGradient)"/>
  
  <!-- 网格背景层 -->
  <rect width="1920" height="1080" fill="url(#grid)" opacity="0.3"/>
  
  <!-- 动态光效层 -->
  <ellipse cx="400" cy="300" rx="600" ry="300" fill="url(#lightGradient1)" opacity="0.6">
    <animateTransform attributeName="transform" type="translate" values="0,0; 80,50; 0,0" dur="15s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="10s" repeatCount="indefinite"/>
  </ellipse>
  
  <ellipse cx="1500" cy="700" rx="500" ry="250" fill="url(#lightGradient2)" opacity="0.5">
    <animateTransform attributeName="transform" type="translate" values="0,0; -60,40; 0,0" dur="18s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.3;0.7;0.3" dur="12s" repeatCount="indefinite"/>
  </ellipse>
  
  <!-- 中央光效 -->
  <ellipse cx="960" cy="540" rx="800" ry="400" fill="url(#lightGradient1)" opacity="0.3">
    <animateTransform attributeName="transform" type="translate" values="0,0; 30,-30; 0,0" dur="20s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="14s" repeatCount="indefinite"/>
  </ellipse>
  
  <!-- 浮动粒子 -->
  <g filter="url(#glow)">
    <circle cx="200" cy="200" r="3" fill="#4facfe" opacity="0.8">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="4s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate" values="0,0; 40,30; 0,0" dur="8s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="1700" cy="300" r="2.5" fill="#00f2fe" opacity="0.7">
      <animate attributeName="opacity" values="0.3;0.7;0.3" dur="5s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate" values="0,0; -30,50; 0,0" dur="10s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="300" cy="800" r="2" fill="#667eea" opacity="0.6">
      <animate attributeName="opacity" values="0.2;0.6;0.2" dur="6s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate" values="0,0; 50,20; 0,0" dur="12s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="1600" cy="800" r="3.5" fill="#a855f7" opacity="0.5">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="7s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate" values="0,0; -40,25; 0,0" dur="9s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- AutoClip 主标题 -->
  <text x="960" y="540" text-anchor="middle" dominant-baseline="middle" 
        font-family="Arial, sans-serif" font-size="180" font-weight="bold" 
        fill="url(#textGradient)" filter="url(#textGlow)">
    AutoClip
  </text>
  
  <!-- 装饰线条 -->
  <g opacity="0.4">
    <line x1="200" y1="540" x2="720" y2="540" stroke="url(#textGradient)" stroke-width="2" opacity="0.6">
      <animate attributeName="opacity" values="0.3;0.6;0.3" dur="8s" repeatCount="indefinite"/>
    </line>
    
    <line x1="1200" y1="540" x2="1720" y2="540" stroke="url(#textGradient)" stroke-width="2" opacity="0.6">
      <animate attributeName="opacity" values="0.3;0.6;0.3" dur="8s" repeatCount="indefinite" begin="4s"/>
    </line>
  </g>
  
  <!-- 装饰圆环 -->
  <g opacity="0.3">
    <circle cx="960" cy="540" r="300" fill="none" stroke="#4facfe" stroke-width="1" opacity="0.3">
      <animate attributeName="stroke-opacity" values="0.1;0.3;0.1" dur="10s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="rotate" values="0 960 540; 360 960 540" dur="30s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="960" cy="540" r="400" fill="none" stroke="#00f2fe" stroke-width="0.5" opacity="0.2">
      <animate attributeName="stroke-opacity" values="0.1;0.2;0.1" dur="12s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="rotate" values="360 960 540; 0 960 540" dur="40s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 底部渐变遮罩 -->
  <defs>
    <linearGradient id="bottomMask" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="transparent" stop-opacity="0"/>
      <stop offset="100%" stop-color="#0f0f23" stop-opacity="0.8"/>
    </linearGradient>
  </defs>
  <rect x="0" y="800" width="1920" height="280" fill="url(#bottomMask)"/>
</svg>